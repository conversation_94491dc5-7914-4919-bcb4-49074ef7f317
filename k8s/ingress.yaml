apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ctint-visual-tracking-platform-ingress
  namespace: cdss-frontend
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  rules:
    - http:
        paths:
          - path: /tracking(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: ctint-visual-tracking-platform-service
                port:
                  number: 6752
          - path: /api/v1(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: ctint-visual-tracking-platform-service
                port:
                  number: 6752